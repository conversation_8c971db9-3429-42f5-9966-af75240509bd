audioFeedbackService.ts:36 🎵 AudioFeedbackService: Preloading audio files...
audioFeedbackService.ts:44 💎 Preloaded gem sound: common -> /audio/gems/gem-common.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: uncommon -> /audio/gems/gem-uncommon.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: rare -> /audio/gems/gem-rare.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: epic -> /audio/gems/gem-epic.mp3
audioFeedbackService.ts:44 💎 Preloaded gem sound: legendary -> /audio/gems/gem-legendary.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: unlock -> /audio/achievements/achievement-unlock.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: rare -> /audio/achievements/achievement-rare.mp3
audioFeedbackService.ts:53 🏆 Preloaded achievement sound: legendary -> /audio/achievements/achievement-legendary.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: correct -> /audio/sfx/correct-answer.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: incorrect -> /audio/sfx/wrong-answer.mp3
audioFeedbackService.ts:62 🔊 Preloaded feedback sound: levelComplete -> /audio/battle/victory.mp3
audioFeedbackService.ts:65 ✅ AudioFeedbackService: Audio preloading complete!
audioFeedbackService.ts:66 📋 Cached audio keys: (11) ['gem-common', 'gem-uncommon', 'gem-rare', 'gem-epic', 'gem-legendary', 'achievement-unlock', 'achievement-rare', 'achievement-legendary', 'feedback-correct', 'feedback-incorrect', 'feedback-levelComplete']
 Cart items changed: []
 Loading cart from localStorage: []
 Starting auth initialization...
 Fetching current session...
 Cart items changed: []
 Loading cart from localStorage: []
 Starting auth initialization...
 Fetching current session...
 ClientLayout - pathname: /games/vocab-master
 ClientLayout - isClient: false
 ClientLayout - isOnStudentSubdomain: false
 ClientLayout - pathname: /games/vocab-master
 ClientLayout - isClient: false
 ClientLayout - isOnStudentSubdomain: false
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🎯 Vocabulary params: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: '', selectedSubcategory: '', selectedLevel: 'KS3', presetConfig: null}
 🎯 Vocabulary params: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: '', selectedSubcategory: '', selectedLevel: 'KS3', presetConfig: null}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 Auth state change event: SIGNED_IN Session: true
 🔄 Preset config effect running: {presetConfig: null, isInitialized: false}
 ❌ No preset config available
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
 🔄 URL params useEffect running. Current gameState: launcher
 🔄 isAssignmentMode: false
 🔄 searchParams: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets', theme: 'default', …}
 🔍 VocabMaster checking URL params: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets'}
 ✅ Found URL parameters, pre-setting VocabMaster filters...
 🚀 Pre-setting VocabMaster config: {language: 'spanish', curriculumLevel: 'KS3', categoryId: 'identity_personal_life', subcategoryId: 'pets', customMode: false}
 🚀 Setting gameState to launcher (with params)
 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: true, userId: 'demo-user-id', isDemo: true}
 🎮 Demo mode detected - skipping service initialization
 ClientLayout - hostname: localhost
 ClientLayout - isStudentDomain: false
 Cart items changed: []
 🔄 Preset config effect running: {presetConfig: null, isInitialized: false}
 ❌ No preset config available
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterWrapper.tsx:59 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets', theme: 'default', …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets'}
UnifiedVocabMasterWrapper.tsx:72 ✅ Found URL parameters, pre-setting VocabMaster filters...
UnifiedVocabMasterWrapper.tsx:91 🚀 Pre-setting VocabMaster config: {language: 'spanish', curriculumLevel: 'KS3', categoryId: 'identity_personal_life', subcategoryId: 'pets', customMode: false}
UnifiedVocabMasterWrapper.tsx:94 🚀 Setting gameState to launcher (with params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: true, userId: 'demo-user-id', isDemo: true}
UnifiedVocabMasterWrapper.tsx:128 🎮 Demo mode detected - skipping service initialization
ClientLayout.tsx:20 ClientLayout - hostname: localhost
ClientLayout.tsx:21 ClientLayout - isStudentDomain: false
vocab-master:1 Denying load of chrome-extension://aggiiclaiamajehmlfpkjmlbadmkledi/popup.js. Resources must be listed in the web_accessible_resources manifest key in order to be loaded by pages outside the extension.
vocab-master:1 Denying load of chrome-extension://aggiiclaiamajehmlfpkjmlbadmkledi/tat_popup.js. Resources must be listed in the web_accessible_resources manifest key in order to be loaded by pages outside the extension.
ClientLayout.tsx:29 ClientLayout - pathname: /games/vocab-master
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
ClientLayout.tsx:29 ClientLayout - pathname: /games/vocab-master
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterLauncher.tsx:233 🎯 Vocabulary params: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: '', selectedSubcategory: '', selectedLevel: 'KS3', presetConfig: null}
UnifiedVocabMasterLauncher.tsx:233 🎯 Vocabulary params: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: '', selectedSubcategory: '', selectedLevel: 'KS3', presetConfig: null}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
contentscript.js:31  GET chrome-extension://invalid/ net::ERR_FAILED
g @ contentscript.js:31
(anonymous) @ contentscript.js:35
e @ contentscript.js:26
t @ contentscript.js:26
setTimeout
(anonymous) @ contentscript.js:26
i @ contentscript.js:26
fireWith @ contentscript.js:26
fire @ contentscript.js:26
i @ contentscript.js:26
fireWith @ contentscript.js:26
ready @ contentscript.js:26
setTimeout
(anonymous) @ contentscript.js:26
(anonymous) @ contentscript.js:15
755 @ contentscript.js:15
k @ contentscript.js:26
(anonymous) @ contentscript.js:26
(anonymous) @ contentscript.js:35
(anonymous) @ contentscript.js:35
UnifiedVocabMasterLauncher.tsx:183 🔄 Preset config effect running: {presetConfig: null, isInitialized: false}
UnifiedVocabMasterLauncher.tsx:197 ❌ No preset config available
useVocabulary.ts:58 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterWrapper.tsx:59 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets', theme: 'default', …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets'}
UnifiedVocabMasterWrapper.tsx:72 ✅ Found URL parameters, pre-setting VocabMaster filters...
UnifiedVocabMasterWrapper.tsx:91 🚀 Pre-setting VocabMaster config: {language: 'spanish', curriculumLevel: 'KS3', categoryId: 'identity_personal_life', subcategoryId: 'pets', customMode: false}
UnifiedVocabMasterWrapper.tsx:94 🚀 Setting gameState to launcher (with params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: true, userId: 'demo-user-id', isDemo: true}
 🎮 Demo mode detected - skipping service initialization
 🔄 Preset config effect running: {presetConfig: null, isInitialized: false}
 ❌ No preset config available
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
 🔄 URL params useEffect running. Current gameState: launcher
 🔄 isAssignmentMode: false
 🔄 searchParams: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets', theme: 'default', …}
 🔍 VocabMaster checking URL params: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets'}
 ✅ Found URL parameters, pre-setting VocabMaster filters...
 🚀 Pre-setting VocabMaster config: {language: 'spanish', curriculumLevel: 'KS3', categoryId: 'identity_personal_life', subcategoryId: 'pets', customMode: false}
 🚀 Setting gameState to launcher (with params)
 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: false, hasUnifiedUser: true, userId: 'demo-user-id', isDemo: true}
 🎮 Demo mode detected - skipping service initialization
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔄 Preset config effect running: {presetConfig: {…}, isInitialized: true}
 ✅ Applying preset config: {language: 'spanish', curriculumLevel: 'KS3', categoryId: 'identity_personal_life', subcategoryId: 'pets', customMode: false}
 ✅ State updated to: {language: 'spanish', category: 'identity_personal_life', subcategory: 'pets'}
 🎯 Vocabulary params: {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: 'identity_personal_life', selectedSubcategory: 'pets', selectedLevel: 'KS3', presetConfig: {…}}
 🎯 Vocabulary params: {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: 'identity_personal_life', selectedSubcategory: 'pets', selectedLevel: 'KS3', presetConfig: {…}}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 Adding category filter: identity_personal_life
 Adding subcategory filter: pets
 🔍 About to execute query with filters: {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 Session fetched, updating auth state... true
 [Vercel Web Analytics] Debug mode is enabled by default in development. No requests will be sent to the server.
 [Vercel Web Analytics] Running queued event pageview {route: '/games/vocab-master', path: '/games/vocab-master'}
 [Vercel Web Analytics] Running queued event pageview {route: '/games/vocab-master', path: '/games/vocab-master'}
 [Vercel Web Analytics] [pageview] http://localhost:3000/games/vocab-master?lang=es&level=KS3&cat=identity_personal_life&subcat=pets&theme=default {o: 'http://localhost:3000/games/vocab-master?lang=es&l…=identity_personal_life&subcat=pets&theme=default', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: 1754515484570, …}
script.debug.js:1 [Vercel Web Analytics] [pageview] http://localhost:3000/games/vocab-master?lang=es&level=KS3&cat=identity_personal_life&subcat=pets&theme=default {o: 'http://localhost:3000/games/vocab-master?lang=es&l…=identity_personal_life&subcat=pets&theme=default', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: 1754515484570, …}
AuthProvider.tsx:242 Session fetched, updating auth state... true
AuthProvider.tsx:150 updateAuthState called with session: true
AuthProvider.tsx:156 Getting user data for: <EMAIL>
AuthProvider.tsx:102 Setting admin role for email: <EMAIL>
AuthProvider.tsx:104 getUserData returning for admin: {role: 'admin', hasSubscription: true}
AuthProvider.tsx:158 Setting user role in state: admin
AuthProvider.tsx:159 Setting subscription status: true
AuthProvider.tsx:171 updateAuthState completed
AuthProvider.tsx:252 Auth initialization completed successfully
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: true, hasUnifiedUser: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', isDemo: false}
UnifiedVocabMasterWrapper.tsx:136 ✅ Initializing VocabMaster services for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
CartContext.tsx:208 Starting cart sync with server for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
AuthProvider.tsx:273 Auth state change event: INITIAL_SESSION Session: true
AuthProvider.tsx:150 updateAuthState called with session: true
AuthProvider.tsx:156 Getting user data for: <EMAIL>
AuthProvider.tsx:102 Setting admin role for email: <EMAIL>
AuthProvider.tsx:104 getUserData returning for admin: {role: 'admin', hasSubscription: true}
AuthProvider.tsx:158 Setting user role in state: admin
AuthProvider.tsx:159 Setting subscription status: true
AuthProvider.tsx:171 updateAuthState completed
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: true, hasUnifiedUser: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', isDemo: false}
UnifiedVocabMasterWrapper.tsx:136 ✅ Initializing VocabMaster services for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
CartContext.tsx:226 Server cart data: []
CartContext.tsx:227 Current local cart: []
CartContext.tsx:238 Both local and server carts are empty, skipping sync
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
useVocabulary.ts:114 ✅ Fetched vocabulary: 9 items for language: es category: identity_personal_life subcategory: pets
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
MainNavigation.tsx:67 Auth state changed in MainNavigation: {isAuthenticated: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', email: '<EMAIL>', role: 'admin'}
MainNavigation.tsx:67 Auth state changed in MainNavigation: {isAuthenticated: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', email: '<EMAIL>', role: 'admin'}
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
ClientLayout.tsx:29 ClientLayout - pathname: /account
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
ClientLayout.tsx:29 ClientLayout - pathname: /account
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
ClientLayout.tsx:29 ClientLayout - pathname: /account
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
ClientLayout.tsx:29 ClientLayout - pathname: /account
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
script.debug.js:1 [Vercel Web Analytics] [pageview] http://localhost:3000/account {o: 'http://localhost:3000/account', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: *************, …}
fetch.js:30  GET https://xetsvpfunazwkontdpdh.supabase.co/rest/v1/school_codes?select=code%2Cschool_initials&school_initials=eq.FCC 406 (Not Acceptable)
eval @ fetch.js:30
eval @ fetch.js:51
fulfilled @ fetch.js:11
Promise.then
step @ fetch.js:13
eval @ fetch.js:14
__awaiter @ fetch.js:10
eval @ fetch.js:41
then @ PostgrestBuilder.js:65
fetch.js:30  GET https://xetsvpfunazwkontdpdh.supabase.co/rest/v1/school_codes?select=code%2Cschool_initials&school_initials=eq.FCC 406 (Not Acceptable)
eval @ fetch.js:30
eval @ fetch.js:51
fulfilled @ fetch.js:11
Promise.then
step @ fetch.js:13
eval @ fetch.js:14
__awaiter @ fetch.js:10
eval @ fetch.js:41
then @ PostgrestBuilder.js:65
ClientLayout.tsx:29 ClientLayout - pathname: /games
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
ClientLayout.tsx:29 ClientLayout - pathname: /games
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
ClientLayout.tsx:29 ClientLayout - pathname: /games
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
ClientLayout.tsx:29 ClientLayout - pathname: /games
ClientLayout.tsx:30 ClientLayout - isClient: true
ClientLayout.tsx:31 ClientLayout - isOnStudentSubdomain: false
MainNavigation.tsx:12 MainNavigation component loaded!
MainNavigation.tsx:12 MainNavigation component loaded!
script.debug.js:1 [Vercel Web Analytics] [pageview] http://localhost:3000/games {o: 'http://localhost:3000/games', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: 1754515487653, …}
hook.js:608 Image with src "/images/games/word-blast.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/hangman.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/sentence-towers.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/speed-builder.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/memory-match.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/noughts-and-crosses.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/conjugation-duel.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/word-scramble.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/case-file-translator.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
hook.js:608 Image with src "/images/games/detective-listening.jpg" has "fill" but is missing "sizes" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes
overrideMethod @ hook.js:608
warnOnce @ warn-once.js:16
eval @ image-component.js:90
Promise.then
handleLoading @ image-component.js:37
onLoad @ image-component.js:198
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:20703
executeDispatch @ react-dom.development.js:32128
processDispatchQueueItemsInOrder @ react-dom.development.js:32160
processDispatchQueue @ react-dom.development.js:32173
dispatchEventsForPlugins @ react-dom.development.js:32184
eval @ react-dom.development.js:32374
batchedUpdates$1 @ react-dom.development.js:24953
batchedUpdates @ react-dom.development.js:28844
dispatchEventForPluginEventSystem @ react-dom.development.js:32373
dispatchEvent @ react-dom.development.js:30141
page.tsx:415 🚀 Navigating to: /games/vocab-master?lang=es&level=KS3&cat=identity_personal_life&subcat=pets&theme=default
 ClientLayout - pathname: /games/vocab-master
 ClientLayout - isClient: true
 ClientLayout - isOnStudentSubdomain: false
 ClientLayout - pathname: /games/vocab-master
 ClientLayout - isClient: true
 ClientLayout - isOnStudentSubdomain: false
 MainNavigation component loaded!
 MainNavigation component loaded!
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: false, vocabularyCount: 0, hasGameService: false, isAssignmentMode: false, …}
 🎯 Vocabulary params: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: '', selectedSubcategory: '', selectedLevel: 'KS3', presetConfig: null}
 🎯 Vocabulary params: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: '', selectedSubcategory: '', selectedLevel: 'KS3', presetConfig: null}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔄 Preset config effect running: {presetConfig: null, isInitialized: false}
 ❌ No preset config available
 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets', theme: 'default', …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets'}
UnifiedVocabMasterWrapper.tsx:72 ✅ Found URL parameters, pre-setting VocabMaster filters...
UnifiedVocabMasterWrapper.tsx:91 🚀 Pre-setting VocabMaster config: {language: 'spanish', curriculumLevel: 'KS3', categoryId: 'identity_personal_life', subcategoryId: 'pets', customMode: false}
UnifiedVocabMasterWrapper.tsx:94 🚀 Setting gameState to launcher (with params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: true, hasUnifiedUser: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', isDemo: false}
UnifiedVocabMasterWrapper.tsx:136 ✅ Initializing VocabMaster services for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
UnifiedVocabMasterLauncher.tsx:183 🔄 Preset config effect running: {presetConfig: null, isInitialized: false}
UnifiedVocabMasterLauncher.tsx:197 ❌ No preset config available
useVocabulary.ts:58 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: undefined, subcategoryId: undefined, curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
UnifiedVocabMasterWrapper.tsx:59 🔄 URL params useEffect running. Current gameState: launcher
UnifiedVocabMasterWrapper.tsx:60 🔄 isAssignmentMode: false
UnifiedVocabMasterWrapper.tsx:61 🔄 searchParams: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets', theme: 'default', …}
UnifiedVocabMasterWrapper.tsx:69 🔍 VocabMaster checking URL params: {lang: 'es', level: 'KS3', cat: 'identity_personal_life', subcat: 'pets'}
UnifiedVocabMasterWrapper.tsx:72 ✅ Found URL parameters, pre-setting VocabMaster filters...
UnifiedVocabMasterWrapper.tsx:91 🚀 Pre-setting VocabMaster config: {language: 'spanish', curriculumLevel: 'KS3', categoryId: 'identity_personal_life', subcategoryId: 'pets', customMode: false}
UnifiedVocabMasterWrapper.tsx:94 🚀 Setting gameState to launcher (with params)
UnifiedVocabMasterWrapper.tsx:114 🔧 VocabMaster service initialization: {hasSupabase: true, hasUser: true, hasUnifiedUser: true, userId: '9efcdbe9-7116-4bb7-a696-4afb0fb34e4c', isDemo: false}
UnifiedVocabMasterWrapper.tsx:136 ✅ Initializing VocabMaster services for user: 9efcdbe9-7116-4bb7-a696-4afb0fb34e4c
script.debug.js:1 [Vercel Web Analytics] [pageview] http://localhost:3000/games/vocab-master?lang=es&level=KS3&cat=identity_personal_life&subcat=pets&theme=default {o: 'http://localhost:3000/games/vocab-master?lang=es&l…=identity_personal_life&subcat=pets&theme=default', sv: '0.1.3', sdkn: '@vercel/analytics/next', sdkv: '1.5.0', ts: 1754515495072, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 0, hasGameService: true, isAssignmentMode: false, …}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: undefined, subcategoryId: undefined, difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:183 🔄 Preset config effect running: {presetConfig: {…}, isInitialized: true}
UnifiedVocabMasterLauncher.tsx:185 ✅ Applying preset config: {language: 'spanish', curriculumLevel: 'KS3', categoryId: 'identity_personal_life', subcategoryId: 'pets', customMode: false}
UnifiedVocabMasterLauncher.tsx:191 ✅ State updated to: {language: 'spanish', category: 'identity_personal_life', subcategory: 'pets'}
UnifiedVocabMasterLauncher.tsx:233 🎯 Vocabulary params: {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: 'identity_personal_life', selectedSubcategory: 'pets', selectedLevel: 'KS3', presetConfig: {…}}
UnifiedVocabMasterLauncher.tsx:233 🎯 Vocabulary params: {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', curriculumLevel: 'KS3', limit: 500, …} from state: {selectedLanguage: 'spanish', selectedCategory: 'identity_personal_life', selectedSubcategory: 'pets', selectedLevel: 'KS3', presetConfig: {…}}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:58 useVocabularyByCategory: Starting fetch with params: {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:77 Adding category filter: identity_personal_life
useVocabulary.ts:83 Adding subcategory filter: pets
useVocabulary.ts:99 🔍 About to execute query with filters: {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', curriculumLevel: 'KS3', difficultyLevel: 'beginner'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:114 ✅ Fetched vocabulary: 9 items for language: es category: identity_personal_life subcategory: pets
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:114 ✅ Fetched vocabulary: 1000 items for language: es category: undefined subcategory: undefined
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterLauncher.tsx:469 🎯 Starting VocabMaster with context: {mode: 'learn_new', language: 'spanish', level: 'KS3', category: 'identity_personal_life', subcategory: 'pets', …}
UnifiedVocabMasterWrapper.tsx:178 🎮 VocabMaster handleGameStart called: {mode: 'learn_new', vocabularyCount: 20, config: {…}, hasGameService: true, hasSelectedConfig: true, …}
UnifiedVocabMasterWrapper.tsx:195 ✅ VocabMaster vocabulary set: 20 items
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 20, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'launcher', hasSelectedConfig: true, vocabularyCount: 20, hasGameService: true, isAssignmentMode: false, …}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
useVocabulary.ts:39 🔥🔥🔥 VOCABULARY HOOK CALLED 🔥🔥🔥 {language: 'es', categoryId: 'identity_personal_life', subcategoryId: 'pets', difficultyLevel: 'beginner', curriculumLevel: 'KS3'}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'playing', hasSelectedConfig: true, vocabularyCount: 20, hasGameService: true, isAssignmentMode: false, …}
UnifiedVocabMasterWrapper.tsx:445 🔍 VocabMaster render state: {gameState: 'playing', hasSelectedConfig: true, vocabularyCount: 20, hasGameService: true, isAssignmentMode: false, …}
